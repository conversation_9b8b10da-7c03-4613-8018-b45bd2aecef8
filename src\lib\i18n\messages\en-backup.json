{"common": {"_context": "General UI elements used throughout the application - buttons, labels, status messages, navigation", "buttons": {"save": "Save", "cancel": "Cancel", "continue": "Continue", "back": "Back", "next": "Next", "finish": "Finish", "reset": "Reset", "loading": "Loading...", "retry": "Try Again", "submit": "Submit", "delete": "Delete", "edit": "Edit", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "previous": "Previous", "processing": "Processing..."}, "labels": {"optional": "Optional", "required": "Required", "recommended": "Recommended", "name": "Name", "email": "Email", "password": "Password", "search": "Search", "filter": "Filter", "sort": "Sort", "language": "Language", "age": "Age", "gender": "Gender"}, "errors": {"general": "Something went wrong. Please try again.", "network": "Network error. Please check your connection.", "validation": "Please check your input and try again.", "notFound": "Not found", "unauthorized": "You are not authorized to perform this action", "forbidden": "Access denied"}, "navigation": {"home": "Home", "dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register"}, "status": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "saving": "Saving...", "ready": "Ready", "lastSaved": "Last saved: {time}", "autoSaving": "Auto-saving..."}}, "auth": {"login": {"_context": "User login form - welcoming users back to their account", "title": "Welcome Back", "subtitle": "Sign in to your account", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "forgotPassword": "Forgot your password?", "noAccount": "Don't have an account?", "signUp": "Sign up"}, "register": {"_context": "User registration form - creating new accounts", "title": "Create Account", "subtitle": "Join us today", "namePlaceholder": "Enter your full name", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Create a password", "confirmPasswordPlaceholder": "Confirm your password", "hasAccount": "Already have an account?", "signIn": "Sign in"}, "forgotPassword": {"_context": "Password reset form - helping users recover their accounts", "title": "Reset Password", "subtitle": "Enter your email to receive reset instructions", "emailPlaceholder": "Enter your email", "backToLogin": "Back to login"}}, "dashboard": {"_context": "Main dashboard interface - welcome messages, navigation, statistics", "title": "Dashboard", "welcome": "Welcome back", "quickActions": "Quick Actions", "recentActivity": "Recent Activity", "stats": "Statistics"}, "profile": {"_context": "User profile and settings management", "title": "Profile", "personalInfo": "Personal Information", "preferences": "Preferences", "language": "Language", "timezone": "Timezone", "notifications": "Notifications"}, "createRecipe": {"_context": "Essential oil recipe creation wizard - multi-step process with AI analysis", "title": "Essential Oil Recipe Creator", "subtitle": "Create personalized aromatherapy recipes", "chatInput": {"_context": "Chat-style input for health concerns - conversational interface for recipe creation", "title": "Create Your Recipe", "description": "Tell us about your health concern and we'll create a personalized essential oil recipe for you", "subtitle": "Be as specific as possible for the best recommendations", "placeholder": "Describe your health concern in detail...", "examples": {"headaches": "I have chronic headaches that worsen with stress and lack of sleep", "digestive": "Experiencing digestive issues with bloating after meals", "sleep": "Di<PERSON><PERSON><PERSON>y falling asleep and staying asleep, feeling anxious at bedtime", "tension": "Muscle tension in shoulders and neck from desk work"}, "characterCounter": "{count}/500", "validation": {"tooShort": "Please provide more details (minimum 3 characters)", "tooLong": "Please keep under 500 characters"}}, "healthConcern": {"_context": "Health concern form - detailed medical information collection for aromatherapy recommendations", "title": "What's your health concern?", "description": "Describe your health concern in detail. The more specific you are, the better we can help you find the right essential oils.", "label": "Health Concern", "placeholder": "For example: I've been experiencing anxiety and stress from work, especially during busy periods. I have trouble sleeping and feel tense throughout the day...", "characterCounter": "{count}/500", "minCharacters": "Minimum 3 characters required", "examples": {"title": "💡 Examples of good health concerns:", "headaches": "I have chronic headaches that worsen with stress and lack of sleep", "digestive": "Experiencing digestive issues with bloating after meals", "sleep": "Di<PERSON><PERSON><PERSON>y falling asleep and staying asleep, feeling anxious at bedtime", "tension": "Muscle tension in shoulders and neck from desk work"}, "readyToContinue": "✓ Ready to continue"}, "demographics": {"_context": "Demographics form - personal information for personalized essential oil recommendations", "title": "Tell us about yourself", "description": "This information helps us provide more personalized essential oil recommendations based on your demographics.", "gender": {"label": "Gender", "options": {"male": "Male", "female": "Female"}}, "ageCategory": {"label": "Age Category", "options": {"child": {"label": "Child (0-12)", "description": "Pediatric considerations"}, "teen": {"label": "Teen (13-17)", "description": "Adolescent development"}, "adult": {"label": "Adult (18-64)", "description": "General adult population"}, "senior": {"label": "Senior (65+)", "description": "Elderly considerations"}}}, "specificAge": {"label": "Specific Age: {age}", "placeholder": "Select category", "instructions": {"withCategory": "Use the slider or enter your age directly.", "selectFirst": "Please select an age category first."}}, "readyToContinue": "✓ Ready to continue"}, "steps": {"_context": "Recipe creation step navigation and descriptions", "healthConcern": {"title": "Health Concern", "description": "Tell us about your health concern or goal", "placeholder": "Describe your health concern, symptoms, or wellness goals...", "examples": "Examples: headaches, stress, sleep issues, skin problems"}, "demographics": {"title": "Demographics", "description": "Help us personalize your recommendations"}, "causes": {"title": "Potential Causes", "description": "Select what might be contributing to your concern", "loading": "Analyzing your health concern...", "selectMultiple": "Select all that apply", "noResults": "No potential causes found. Please try rephrasing your health concern."}, "symptoms": {"title": "Symptoms", "description": "Select the symptoms you're experiencing", "loading": "Finding related symptoms...", "selectMultiple": "Select all that apply", "noResults": "No symptoms found. Please review your selected causes."}, "properties": {"title": "Therapeutic Properties", "description": "Based on your selections, these properties may help", "loading": "Determining therapeutic properties...", "oilSuggestions": "Oil Suggestions", "loadingOils": "Finding suitable oils...", "noOils": "No oils found for this property"}}, "navigation": {"_context": "Recipe creation progress and navigation elements", "breadcrumb": {"step": "Step {current} of {total}", "completed": "Completed"}, "progress": "{completed} of {total} steps completed"}, "validation": {"_context": "Form validation messages for recipe creation process", "healthConcern": "Please describe your health concern", "demographics": "Please complete your demographic information", "causes": "Please select at least one potential cause", "symptoms": "Please select at least one symptom", "healthConcernRequired": "Health concern is required to proceed"}, "streaming": {"_context": "AI analysis status messages during recipe creation", "analyzing": "Analyzing your information...", "analyzingDescription": "We are analyzing your demographics and health concern to identify potential causes and provide personalized recommendations.", "error": "Analysis failed. Please try again.", "completed": "Analysis completed successfully!", "found": "found", "showingLatest": "Showing latest {maxVisible} of {total}", "terminal": {"streaming": "streaming", "potentialCausesAnalysis": "Potential Causes Analysis", "potentialSymptomsAnalysis": "Potential Symptoms Analysis", "therapeuticPropertiesAnalysis": "Therapeutic Properties Analysis", "essentialOilsAnalysis": "Essential Oils Analysis", "causesSubtitle": "Understanding factors that may contribute to your symptoms.", "symptomsSubtitle": "Identifying symptoms that may manifest based on your selected causes.", "propertiesSubtitle": "Finding therapeutic properties to address your symptoms.", "oilsSubtitle": "Recommending essential oils with the identified properties."}, "loading": {"analyzingDemographics": "analyzing demographics...", "analyzingCauses": "analyzing selected causes...", "analyzingSymptoms": "analyzing symptoms...", "analyzingProperties": "analyzing properties..."}, "progress": {"analyzingMoreCauses": "Analyzing more potential causes...", "analyzingMoreSymptoms": "Analyzing more potential symptoms...", "analyzingMoreProperties": "Analyzing more therapeutic properties...", "analyzingMoreOils": "Analyzing more essential oils..."}, "status": {"aiProcessing": "AI is processing your information to find additional insights", "liveAnalysis": "Live analysis in progress"}}}, "oils": {"_context": "Essential oil information - safety guidelines and therapeutic properties", "safety": {"_context": "Essential oil safety information and warnings", "title": "Safety Information", "pregnancy": "Pregnancy Safe", "children": "Child Safe", "sensitive": "Sensitive Skin Safe", "dilution": "Dilution Required", "photosensitive": "Photosensitive - Avoid Sun"}, "properties": {"_context": "Therapeutic properties of essential oils for health benefits", "antimicrobial": "Antimicrobial", "antiInflammatory": "Anti-inflammatory", "calming": "Calming", "energizing": "Energizing", "analgesic": "Pain Relief"}}, "chat": {"_context": "AI chat interface for essential oil questions and guidance", "title": "AI Chat", "placeholder": "Ask me anything about essential oils...", "send": "Send", "thinking": "Thinking...", "newChat": "New Chat", "clearHistory": "Clear History"}}