{"_ai_translation_instructions": {"target_language": "Spanish (Latin America)", "context": "Essential oils and aromatherapy web application", "tone": "Professional but friendly, health-focused", "notes": "Maintain medical accuracy. Use Latin American Spanish terms."}, "common": {"_context": "General UI elements used throughout the application - buttons, labels, status messages, navigation", "buttons": {"save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "back": "Atrás", "next": "Siguient<PERSON>", "finish": "Finalizar", "reset": "Restablecer", "loading": "Cargando...", "retry": "Intentar de Nuevo", "submit": "Enviar", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "yes": "Sí", "no": "No", "previous": "Anterior", "processing": "Procesando..."}, "labels": {"optional": "Opcional", "required": "Requerido", "recommended": "Recomendado", "name": "Nombre", "email": "Correo electrónico", "password": "Contraseña", "search": "Buscar", "filter": "Filtrar", "sort": "Ordenar", "language": "Idioma", "age": "Edad", "gender": "<PERSON><PERSON><PERSON>"}, "errors": {"general": "Algo salió mal. <PERSON><PERSON> favor, inténtalo de nuevo.", "network": "Error de red. Verifica tu conexión.", "validation": "Verifica tu información e inténtalo de nuevo.", "notFound": "No encontrado", "unauthorized": "No estás autorizado para realizar esta acción", "forbidden": "Acceso denegado"}, "navigation": {"home": "<PERSON><PERSON>o", "dashboard": "Panel", "profile": "Perfil", "settings": "Configuración", "logout": "<PERSON><PERSON><PERSON>", "login": "In<PERSON><PERSON>", "register": "Registrarse"}, "status": {"success": "Éxito", "error": "Error", "warning": "Advertencia", "info": "Información", "saving": "Guardando...", "ready": "Listo", "lastSaved": "<PERSON><PERSON><PERSON> guardado: {time}", "autoSaving": "Guardado automático..."}}, "auth": {"login": {"_context": "User login form - welcoming users back to their account", "title": "Bienvenido de Vuelta", "subtitle": "Inicia sesión en tu cuenta", "emailPlaceholder": "Ingresa tu correo electrónico", "passwordPlaceholder": "Ingresa tu contraseña", "forgotPassword": "¿Olvidaste tu contraseña?", "noAccount": "¿No tienes una cuenta?", "signUp": "Regístrate"}, "register": {"_context": "User registration form - creating new accounts", "title": "<PERSON><PERSON><PERSON>", "subtitle": "Únete a nosotros hoy", "namePlaceholder": "Ingresa tu nombre completo", "emailPlaceholder": "Ingresa tu correo electrónico", "passwordPlaceholder": "Crea una contraseña", "confirmPasswordPlaceholder": "Confirma tu contraseña", "hasAccount": "¿Ya tienes una cuenta?", "signIn": "In<PERSON><PERSON>"}, "forgotPassword": {"_context": "Password reset form - helping users recover their accounts", "title": "Restable<PERSON>", "subtitle": "Ingresa tu correo electrónico para recibir instrucciones de restablecimiento", "emailPlaceholder": "Ingresa tu correo electrónico", "backToLogin": "Volver al inicio de sesión"}}, "dashboard": {"_context": "Main dashboard interface - welcome messages, navigation, statistics", "title": "Panel", "welcome": "Bienvenido de vuelta", "quickActions": "Acciones Rápidas", "recentActivity": "Actividad Reciente", "stats": "Estadísticas"}, "profile": {"_context": "User profile and settings management", "title": "Perfil", "personalInfo": "Información Personal", "preferences": "Preferencias", "language": "Idioma", "timezone": "Zona horaria", "notifications": "Notificaciones"}, "createRecipe": {"_context": "Essential oil recipe creation wizard - multi-step process with AI analysis", "title": "Creador de Recetas de Aceites Esenciales", "subtitle": "Crea recetas personalizadas de aromaterapia", "chatInput": {"_context": "Chat-style input for health concerns - conversational interface for recipe creation", "title": "<PERSON><PERSON><PERSON>", "description": "Cuéntanos sobre tu preocupación de salud y crearemos una receta personalizada de aceites esenciales para ti", "subtitle": "Sé lo más específico posible para las mejores recomendaciones", "placeholder": "Describe tu preocupación de salud en detalle...", "examples": {"headaches": "Tengo dolores de cabeza crónicos que empeoran con el estrés y la falta de sueño", "digestive": "Experimentando problemas digestivos con hinchazón después de las comidas", "sleep": "Dificultad para conciliar el sueño y permanecer dormido, sintiendo ansiedad a la hora de dormir", "tension": "Tensión muscular en hombros y cuello por trabajar en escritorio"}, "characterCounter": "{count}/500", "validation": {"tooShort": "Por favor, proporciona más detalles (mínimo 3 caracteres)", "tooLong": "Por favor, mantén menos de 500 caracteres"}}, "healthConcern": {"_context": "Health concern form - detailed medical information collection for aromatherapy recommendations", "title": "¿Cuál es tu preocupación de salud?", "description": "Describe tu preocupación de salud en detalle. Cuanto más específico seas, mejor podremos ayudarte a encontrar los aceites esenciales correctos.", "label": "Preocupación de Salud", "placeholder": "Por ejemplo: He estado experimentando ansiedad y estrés del trabajo, especialmente durante períodos ocupados. Tengo dificultad para dormir y me siento tenso durante el día...", "characterCounter": "{count}/500", "minCharacters": "Mínimo 3 caracteres requerido", "examples": {"title": "💡 Ejemplos de buenas preocupaciones de salud:", "headaches": "Tengo dolores de cabeza crónicos que empeoran con el estrés y la falta de sueño", "digestive": "Experimentando problemas digestivos con hinchazón después de las comidas", "sleep": "Dificultad para conciliar el sueño y permanecer dormido, sintiendo ansiedad a la hora de dormir", "tension": "Tensión muscular en hombros y cuello por trabajar en escritorio"}, "readyToContinue": "✓ Listo para continuar"}, "demographics": {"_context": "Demographics form - personal information for personalized essential oil recommendations", "title": "Cuéntanos sobre ti", "description": "Esta información nos ayuda a proporcionar recomendaciones más personalizadas de aceites esenciales basadas en tu demografía.", "gender": {"label": "<PERSON><PERSON><PERSON>", "options": {"male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Femenino"}}, "ageCategory": {"label": "Categoría de Edad", "options": {"child": {"label": "<PERSON><PERSON> (0-12)", "description": "Consideraciones pediátricas"}, "teen": {"label": "Adolescente (13-17)", "description": "Desarrollo adolescente"}, "adult": {"label": "<PERSON><PERSON> (18-64)", "description": "Población adulta general"}, "senior": {"label": "Adulto mayor (65+)", "description": "Consideraciones para adultos mayores"}}}, "specificAge": {"label": "<PERSON><PERSON> Específica: {age}", "placeholder": "Selecciona categoría", "instructions": {"withCategory": "Usa el control deslizante o ingresa tu edad directamente.", "selectFirst": "Por favor, selecciona una categoría de edad primero."}}, "readyToContinue": "✓ Listo para continuar"}, "steps": {"_context": "Recipe creation step navigation and descriptions", "healthConcern": {"title": "Preocupación de Salud", "description": "Cuéntanos sobre tu preocupación de salud u objetivo", "placeholder": "Describe tu preocupación de salud, síntomas u objetivos de bienestar...", "examples": "Ejemplos: dolores de cabeza, est<PERSON>s, problemas de sueño, problemas de piel"}, "demographics": {"title": "Demografía", "description": "Ayúdanos a personalizar tus recomendaciones"}, "causes": {"title": "Causas <PERSON>", "description": "Selecciona lo que puede estar contribuyendo a tu preocupación", "loading": "Analizando tu preocupación de salud...", "selectMultiple": "Selecciona todos los que apliquen", "noResults": "No se encontraron causas potenciales. Intenta reformular tu preocupación de salud."}, "symptoms": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Selecciona los síntomas que estás experimentando", "loading": "Encontrando síntomas relacionados...", "selectMultiple": "Selecciona todos los que apliquen", "noResults": "No se encontraron síntomas. Revisa tus causas seleccionadas."}, "properties": {"title": "Propiedades Terapéuticas", "description": "Basado en tus selecciones, estas propiedades pueden ayudar", "loading": "Determinando propiedades terapéuticas...", "oilSuggestions": "Sugerencias de Aceites", "loadingOils": "Encontrando aceites adecuados...", "noOils": "No se encontraron aceites para esta propiedad"}}, "navigation": {"_context": "Recipe creation progress and navigation elements", "breadcrumb": {"step": "Paso {current} de {total}", "completed": "Completado"}, "progress": "{completed} de {total} pasos completados"}, "validation": {"_context": "Form validation messages for recipe creation process", "healthConcern": "Por favor, describe tu preocupación de salud", "demographics": "Por favor, completa tu información demográfica", "causes": "Por favor, selecciona al menos una causa potencial", "symptoms": "Por favor, selecciona al menos un síntoma", "healthConcernRequired": "La preocupación de salud es requerida para continuar"}, "streaming": {"_context": "AI analysis status messages during recipe creation", "analyzing": "Analizando tu información...", "error": "El análisis falló. Por favor, inténtalo de nuevo.", "completed": "¡Análisis completado exitosamente!"}}, "oils": {"_context": "Essential oil information - safety guidelines and therapeutic properties", "safety": {"_context": "Essential oil safety information and warnings", "title": "Información de Seguridad", "pregnancy": "Seguro en el Embarazo", "children": "Seguro para Niños", "sensitive": "Seguro para Piel Sensible", "dilution": "Di<PERSON>ción Requerida", "photosensitive": "Fotosensible - Evitar Sol"}, "properties": {"_context": "Therapeutic properties of essential oils for health benefits", "antimicrobial": "Antimicrobiano", "antiInflammatory": "Antiinflamatorio", "calming": "Calmante", "energizing": "Energizante", "analgesic": "<PERSON><PERSON>"}}, "chat": {"_context": "AI chat interface for essential oil questions and guidance", "title": "Chat IA", "placeholder": "Pregúntame cualquier cosa sobre aceites esenciales...", "send": "Enviar", "thinking": "Pensando...", "newChat": "Nuevo Chat", "clearHistory": "Limpiar Historial"}}