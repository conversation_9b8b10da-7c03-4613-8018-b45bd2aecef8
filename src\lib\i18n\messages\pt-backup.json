{"_ai_translation_instructions": {"target_language": "Portuguese (Brazil)", "context": "Essential oils and aromatherapy web application", "tone": "Professional but friendly, health-focused", "notes": "Maintain medical accuracy. Use Brazilian Portuguese terms."}, "common": {"_context": "General UI elements used throughout the application - buttons, labels, status messages, navigation", "buttons": {"save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "back": "Voltar", "next": "Próximo", "finish": "Finalizar", "reset": "Redefinir", "loading": "Carregando...", "retry": "Tentar Novamente", "submit": "Enviar", "delete": "Excluir", "edit": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "yes": "<PERSON>m", "no": "Não", "previous": "Anterior", "processing": "Processando..."}, "labels": {"optional": "Opcional", "required": "Obrigatório", "recommended": "Recomendado", "name": "Nome", "email": "E-mail", "password": "<PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON><PERSON>", "filter": "Filtrar", "sort": "Ordenar", "language": "Idioma", "age": "<PERSON><PERSON>", "gender": "<PERSON><PERSON><PERSON><PERSON>"}, "errors": {"general": "Algo deu errado. Por favor, tente novamente.", "network": "Erro de rede. Verifique sua conexão.", "validation": "Verifique suas informações e tente novamente.", "notFound": "Não encontrado", "unauthorized": "Você não está autorizado a realizar esta ação", "forbidden": "<PERSON><PERSON>"}, "navigation": {"home": "Início", "dashboard": "<PERSON><PERSON>", "profile": "Perfil", "settings": "Configurações", "logout": "<PERSON><PERSON>", "login": "Entrar", "register": "Cadastrar"}, "status": {"success": "Sucesso", "error": "Erro", "warning": "Aviso", "info": "Informação", "saving": "Salvando...", "ready": "Pronto", "lastSaved": "Último sal<PERSON>: {time}", "autoSaving": "Salvamento automático..."}}, "auth": {"login": {"_context": "User login form - welcoming users back to their account", "title": "Bem-v<PERSON><PERSON> Volta", "subtitle": "Entre na sua conta", "emailPlaceholder": "Digite seu e-mail", "passwordPlaceholder": "Digite sua senha", "forgotPassword": "Esqueceu sua senha?", "noAccount": "Não tem uma conta?", "signUp": "Cadastre-se"}, "register": {"_context": "User registration form - creating new accounts", "title": "<PERSON><PERSON><PERSON>", "subtitle": "Junte-se a nós hoje", "namePlaceholder": "Digite seu nome completo", "emailPlaceholder": "Digite seu e-mail", "passwordPlaceholder": "<PERSON><PERSON> uma senha", "confirmPasswordPlaceholder": "Confirme sua senha", "hasAccount": "Já tem uma conta?", "signIn": "Entrar"}, "forgotPassword": {"_context": "Password reset form - helping users recover their accounts", "title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Digite seu e-mail para receber instruções de redefinição", "emailPlaceholder": "Digite seu e-mail", "backToLogin": "Voltar para o login"}}, "dashboard": {"_context": "Main dashboard interface - welcome messages, navigation, statistics", "title": "<PERSON><PERSON>", "welcome": "Bem-vindo de volta", "quickActions": "Ações <PERSON>", "recentActivity": "Atividade Recente", "stats": "Estatísticas"}, "profile": {"_context": "User profile and settings management", "title": "Perfil", "personalInfo": "Informaçõ<PERSON>", "preferences": "Preferências", "language": "Idioma", "timezone": "<PERSON><PERSON>", "notifications": "Notificações"}, "createRecipe": {"_context": "Essential oil recipe creation wizard - multi-step process with AI analysis", "title": "Criador de Receitas de Óleos Essenciais", "subtitle": "Crie receitas personalizadas de aromaterapia", "chatInput": {"_context": "Chat-style input for health concerns - conversational interface for recipe creation", "title": "Criar <PERSON><PERSON> Receita", "description": "Conte-nos sobre sua preocupação de saúde e criaremos uma receita personalizada de óleos essenciais para você", "subtitle": "Seja o mais específico possível para as melhores recomendações", "placeholder": "Descreva sua preocupação de saúde em detalhes...", "examples": {"headaches": "Tenho dores de cabeça crônicas que pioram com estresse e falta de sono", "digestive": "Experienciando problemas digestivos com inchaço após as refeições", "sleep": "Dificuldade para dormir e permanecer dormindo, sentindo ansiedade na hora de dormir", "tension": "Tensão muscular nos ombros e pescoço por trabalhar na mesa"}, "characterCounter": "{count}/500", "validation": {"tooShort": "Por favor, forne<PERSON> mais de<PERSON> (mínimo 3 caracteres)", "tooLong": "Por favor, mantenha abaixo de 500 caracteres"}}, "healthConcern": {"_context": "Health concern form - detailed medical information collection for aromatherapy recommendations", "title": "Qual é sua preocupação de saúde?", "description": "Descreva sua preocupação de saúde em detalhes. Quanto mais específico você for, melhor poderemos ajudá-lo a encontrar os óleos essenciais certos.", "label": "Preocupação de Saúde", "placeholder": "Por exemplo: Tenho experimentado ansiedade e estresse do trabalho, especialmente durante períodos ocupados. Tenho dificuldade para dormir e me sinto tenso durante o dia...", "characterCounter": "{count}/500", "minCharacters": "Mínimo 3 caracteres obrigatório", "examples": {"title": "💡 Exemplos de boas preocupações de saúde:", "headaches": "Tenho dores de cabeça crônicas que pioram com estresse e falta de sono", "digestive": "Experienciando problemas digestivos com inchaço após as refeições", "sleep": "Dificuldade para dormir e permanecer dormindo, sentindo ansiedade na hora de dormir", "tension": "Tensão muscular nos ombros e pescoço por trabalhar na mesa"}, "readyToContinue": "✓ Pronto para continuar"}, "demographics": {"_context": "Demographics form - personal information for personalized essential oil recommendations", "title": "Conte-nos sobre você", "description": "Essas informações nos ajudam a fornecer recomendações mais personalizadas de óleos essenciais com base em sua demografia.", "gender": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options": {"male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Feminino"}}, "ageCategory": {"label": "Categoria de Idade", "options": {"child": {"label": "Criança (0-12)", "description": "Considerações pediátricas"}, "teen": {"label": "Adolescente (13-17)", "description": "Desenvolvimento adolescente"}, "adult": {"label": "<PERSON><PERSON> (18-64)", "description": "População adulta geral"}, "senior": {"label": "<PERSON><PERSON><PERSON> (65+)", "description": "Considerações para idosos"}}}, "specificAge": {"label": "Idade Específica: {age}", "placeholder": "Selecione categoria", "instructions": {"withCategory": "Use o controle deslizante ou digite sua idade diretamente.", "selectFirst": "Por favor, selecione uma categoria de idade primeiro."}}, "readyToContinue": "✓ Pronto para continuar"}, "steps": {"_context": "Recipe creation step navigation and descriptions", "healthConcern": {"title": "Preocupação de Saúde", "description": "Conte-nos sobre sua preocupação de saúde ou objetivo", "placeholder": "Descreva sua preocupação de saúde, sintomas ou objetivos de bem-estar...", "examples": "Exemplos: dores de cab<PERSON>ça, estresse, problemas de sono, problemas de pele"}, "demographics": {"title": "Demografia", "description": "Ajude-nos a personalizar suas recomendações"}, "causes": {"title": "<PERSON><PERSON><PERSON>", "description": "Selecione o que pode estar contribuindo para sua preocupação", "loading": "Analisando sua preocupação de saúde...", "selectMultiple": "Selecione todos os que se aplicam", "noResults": "Nenhuma causa potencial encontrada. Tente reformular sua preocupação de saúde."}, "symptoms": {"title": "<PERSON><PERSON><PERSON>", "description": "Selecione os sintomas que você está experimentando", "loading": "Encontrando sintomas relacionados...", "selectMultiple": "Selecione todos os que se aplicam", "noResults": "Nenhum sintoma encontrado. Revise suas causas selecionadas."}, "properties": {"title": "Pro<PERSON><PERSON>ades Terapêuticas", "description": "Com base em suas seleções, essas propriedades podem ajudar", "loading": "Determinando propriedades terapêuticas...", "oilSuggestions": "Sugestões de Óleos", "loadingOils": "Encontrando óleos adequados...", "noOils": "Nenhum óleo encontrado para esta propriedade"}}, "navigation": {"_context": "Recipe creation progress and navigation elements", "breadcrumb": {"step": "Etapa {current} de {total}", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "progress": "{completed} de {total} etapas concluídas"}, "validation": {"_context": "Form validation messages for recipe creation process", "healthConcern": "Por favor, descreva sua preocupação de saúde", "demographics": "Por favor, complete suas informações demográficas", "causes": "Por favor, selecione pelo menos uma causa potencial", "symptoms": "Por favor, selecione pelo menos um sintoma", "healthConcernRequired": "Preocupação de saúde é obrigatória para continuar"}, "streaming": {"_context": "AI analysis status messages during recipe creation", "analyzing": "Analisando suas informações...", "analyzingDescription": "Estamos analisando seu perfil e problema de saúde para identificar causas potenciais e fornecer recomendações personalizadas.", "error": "<PERSON><PERSON><PERSON><PERSON> falhou. Por favor, tente novamente.", "completed": "Análise concluída com sucesso!", "found": "encontrados", "showingLatest": "Mostrando os últimos {maxVisible} de {total}", "terminal": {"streaming": "transmissão", "potentialCausesAnalysis": "Análise de Causas Potenciais", "potentialSymptomsAnalysis": "Análise de Sintomas Potenciais", "therapeuticPropertiesAnalysis": "Análise de Propriedades Terapêuticas", "essentialOilsAnalysis": "<PERSON><PERSON><PERSON><PERSON>", "causesSubtitle": "Compreendendo fatores que podem contribuir para seus sintomas.", "symptomsSubtitle": "Identificando sintomas que podem se manifestar com base nas causas selecionadas.", "propertiesSubtitle": "Encontrando propriedades terapêuticas para abordar seus sintomas.", "oilsSubtitle": "Recomendando óleos essenciais com as propriedades identificadas."}, "loading": {"analyzingDemographics": "analisando demografia...", "analyzingCauses": "analisando causas selecionadas...", "analyzingSymptoms": "analisando sintomas...", "analyzingProperties": "analisando propriedades..."}, "progress": {"analyzingMoreCauses": "<PERSON><PERSON><PERSON><PERSON> mais causas potenciais...", "analyzingMoreSymptoms": "<PERSON><PERSON><PERSON><PERSON> mais sintomas potenciais...", "analyzingMoreProperties": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> propriedades terapêuticas...", "analyzingMoreOils": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> es<PERSON>..."}, "status": {"aiProcessing": "IA está processando suas informações para encontrar insights adicionais", "liveAnalysis": "Análise ao vivo em andamento"}}}, "oils": {"_context": "Essential oil information - safety guidelines and therapeutic properties", "safety": {"_context": "Essential oil safety information and warnings", "title": "Informações de Segurança", "pregnancy": "Seguro na Gravidez", "children": "Seguro para Crianças", "sensitive": "Seguro para Pele Sensível", "dilution": "Diluição Necessária", "photosensitive": "Fotossensível - Evite Sol"}, "properties": {"_context": "Therapeutic properties of essential oils for health benefits", "antimicrobial": "Antimicrobiano", "antiInflammatory": "Anti-inflamatório", "calming": "Calmante", "energizing": "Energizante", "analgesic": "<PERSON><PERSON><PERSON>"}}, "chat": {"_context": "AI chat interface for essential oil questions and guidance", "title": "Chat IA", "placeholder": "Pergunte-me qualquer coisa sobre óleos essenciais...", "send": "Enviar", "thinking": "Pensando...", "newChat": "Novo Chat", "clearHistory": "<PERSON><PERSON>"}}